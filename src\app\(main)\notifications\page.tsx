"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import { useSession } from "next-auth/react";

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { toast } from "sonner";

import { NotificationSettings } from "@/components/notifications/notification-settings";
import { NotificationErrorBoundary } from "@/components/notifications/notification-error-boundary";
import { useAuthErrorHandler } from "@/hooks/use-auth-error-handler";

const NotificationsPage: React.FC = () => {
  const { data: session, status } = useSession();

  const { handle401Error } = useAuthErrorHandler();

  // State management - no default values, will be loaded from API
  const [settings, setSettings] = useState<INotificationPreferences | null>(null);
  const [settingsLoading, setSettingsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Request management refs for optimization
  const fetchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const currentRequestRef = useRef<AbortController | null>(null);
  const lastFetchTimeRef = useRef<number>(0);

  // Fetch notification settings with optimization
  const fetchSettings = useCallback(async () => {
    if (!session?.backendTokens?.accessToken) return;

    // Prevent multiple concurrent requests
    if (currentRequestRef.current) {
      currentRequestRef.current.abort();
    }

    // Rate limiting: prevent requests more frequent than every 500ms
    const now = Date.now();
    if (now - lastFetchTimeRef.current < 500) {
      return;
    }
    lastFetchTimeRef.current = now;

    // Create new abort controller for this request
    const abortController = new AbortController();
    currentRequestRef.current = abortController;

    setSettingsLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/notifications/settings", {
        headers: {
          Authorization: `Bearer ${session.backendTokens.accessToken}`,
          "Content-Type": "application/json",
        },
        signal: abortController.signal,
      });

      // Check if request was aborted
      if (abortController.signal.aborted) {
        return;
      }

      const result = await response.json();

      if (response.ok) {
        // Set settings from API response, no fallback defaults
        setSettings(result.data);
        setError(null);
      } else {
        if (response.status === 401) {
          handle401Error();
          return;
        }
        const errorMessage = result.message || "Failed to fetch notification settings";
        setError(errorMessage);
        toast.error(errorMessage);
        // Don't set fallback settings on error - let user see error state
        setSettings(null);
      }
    } catch (error: any) {
      // Don't show error for aborted requests
      if (error.name === 'AbortError') {
        return;
      }

      console.error("Error fetching notification settings:", error);
      const errorMessage = "Failed to fetch notification settings. Please try again.";
      setError(errorMessage);
      toast.error(errorMessage);
      // Don't set fallback settings on error - let user see error state
      setSettings(null);
    } finally {
      // Only update loading state if this request wasn't aborted
      if (!abortController.signal.aborted) {
        setSettingsLoading(false);
      }

      // Clear current request reference
      if (currentRequestRef.current === abortController) {
        currentRequestRef.current = null;
      }
    }
  }, [session, handle401Error]);

  // Debounced fetch function with request deduplication
  const debouncedFetchSettings = useCallback(
    (delay: number = 300) => {
      // Clear existing timeout
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
      }

      // Set new timeout for debounced execution
      fetchTimeoutRef.current = setTimeout(() => {
        fetchSettings();
      }, delay);
    },
    [fetchSettings]
  );

  // Initial data fetch with authentication check
  useEffect(() => {
    if (status === "loading") {
      return; // Still loading session
    }

    if (status === "unauthenticated") {
      return; // Will be handled by auth middleware
    }

    if (status === "authenticated" && session) {
      // Use debounced fetch for initial load
      debouncedFetchSettings(100); // Shorter delay for initial load
    }
  }, [status, session, debouncedFetchSettings]);

  // Cleanup function to cancel ongoing requests and timeouts
  useEffect(() => {
    return () => {
      // Clear timeout
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
      }

      // Abort ongoing request
      if (currentRequestRef.current) {
        currentRequestRef.current.abort();
      }
    };
  }, []);

  // Settings event handlers with debouncing
  const handleSettingsUpdate = useCallback(() => {
    // Use debounced fetch to prevent rapid successive calls
    debouncedFetchSettings(500);
  }, [debouncedFetchSettings]);

  // Retry handler for error states
  const handleRetry = useCallback(() => {
    setError(null);
    debouncedFetchSettings(100);
  }, [debouncedFetchSettings]);

  // Loading state
  if (status === "loading") {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">Loading...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Notifications</h1>
          <p className="text-muted-foreground">Manage notification settings</p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Notification Settings</CardTitle>
        </CardHeader>
        <CardContent>
          {settingsLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading settings...</p>
            </div>
          ) : error ? (
            <div className="text-center py-8 space-y-4">
              <div className="text-destructive">
                <svg
                  className="h-12 w-12 mx-auto mb-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
                <p className="text-lg font-medium">Unable to load notification settings</p>
                <p className="text-sm text-muted-foreground">{error}</p>
              </div>
              <button
                onClick={handleRetry}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                Try Again
              </button>
            </div>
          ) : settings ? (
            <NotificationErrorBoundary>
              <NotificationSettings
                settings={settings}
                onSettingsUpdate={handleSettingsUpdate}
              />
            </NotificationErrorBoundary>
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Loading notification settings...</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default NotificationsPage;
